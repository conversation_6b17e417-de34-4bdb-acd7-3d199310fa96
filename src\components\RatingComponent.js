import React, { useState, useEffect, useCallback, useRef } from "react";
import { ratingAPI } from "../services/api";

const RatingComponent = ({ novelId, authorId, currentUserId }) => {
  const [ratings, setRatings] = useState({
    like: 0,
    dislike: 0,
    hate: 0,
    super_like: 0,
  });
  const [userRating, setUserRating] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [clickedButton, setClickedButton] = useState(null); // 记录被点击的按钮
  const debounceRef = useRef(null); // 防抖引用

  // 评价类型配置
  const ratingTypes = [
    {
      type: "like",
      icon: "👍",
      label: "点赞",
      color: "text-success",
    },
    {
      type: "super_like",
      icon: "❤️",
      label: "超级喜欢",
      color: "text-danger",
    },
    {
      type: "dislike",
      icon: "👎",
      label: "不喜欢",
      color: "text-warning",
    },
    {
      type: "hate",
      icon: "🤮",
      label: "恶心",
      color: "text-dark",
    },
  ];

  const fetchRatingStats = useCallback(async () => {
    try {
      const response = await ratingAPI.getNovelRatingStats(novelId);
      setRatings(response.data);
    } catch (err) {
      console.error("获取评价统计失败", err);
    }
  }, [novelId]);

  const fetchUserRating = useCallback(async () => {
    try {
      const response = await ratingAPI.getUserRating(novelId);
      setUserRating(response.data.rating);
    } catch (err) {
      console.error("获取用户评价失败", err);
    }
  }, [novelId]);

  useEffect(() => {
    fetchRatingStats();
    if (currentUserId) {
      fetchUserRating();
    }
  }, [novelId, currentUserId, fetchRatingStats, fetchUserRating]);

  const handleRating = useCallback(
    async (ratingType) => {
      // 防抖处理
      if (debounceRef.current) {
        clearTimeout(debounceRef.current);
      }

      debounceRef.current = setTimeout(async () => {
        if (!currentUserId) {
          setError("请先登录");
          return;
        }

        if (currentUserId === authorId) {
          setError("不能给自己的作品评价");
          return;
        }

        if (loading) {
          return; // 如果正在加载，忽略点击
        }

        setLoading(true);
        setError("");
        setClickedButton(ratingType); // 记录被点击的按钮

        try {
          let response;
          if (userRating === ratingType) {
            // 如果点击的是当前评价，则删除评价
            response = await ratingAPI.deleteRating(novelId);
            // 使用批量状态更新减少重新渲染
            setUserRating(null);
            setRatings(response.data.stats);
          } else {
            // 否则创建或更新评价
            response = await ratingAPI.upsertRating(novelId, { ratingType });
            // 使用批量状态更新减少重新渲染
            setUserRating(response.data.userRating);
            setRatings(response.data.stats);
          }
        } catch (err) {
          setError(err.response?.data?.msg || "操作失败");
        } finally {
          // 延迟重置loading状态，让用户看到反馈
          setTimeout(() => {
            setLoading(false);
            setClickedButton(null);
          }, 200);
        }
      }, 100); // 100ms 防抖延迟
    },
    [currentUserId, authorId, loading, userRating, novelId]
  );

  return (
    <div className="rating-component">
      {error && <div className="alert alert-danger alert-sm mb-2">{error}</div>}

      <div className="d-flex flex-wrap gap-2">
        {ratingTypes.map((ratingType) => {
          const isActive = userRating === ratingType.type;
          const isClicked = clickedButton === ratingType.type;
          const isDisabled = loading || currentUserId === authorId;

          return (
            <button
              key={ratingType.type}
              className="btn p-0 border-0 bg-transparent"
              onClick={() => handleRating(ratingType.type)}
              disabled={isDisabled}
              title={currentUserId === authorId ? "不能给自己的作品评价" : ""}
              style={{
                outline: "none",
                boxShadow: "none",
                opacity: isDisabled && !isClicked ? 0.6 : 1,
                transform: isClicked ? "scale(0.95)" : "scale(1)",
                transition: "all 0.15s ease",
              }}
            >
              <div
                className="d-flex align-items-center"
                style={{
                  backgroundColor: isActive ? "#87CEEB" : "transparent",
                  borderRadius: "20px",
                  padding: "6px 12px",
                  minWidth: "60px",
                  transition: "all 0.2s ease",
                  cursor: isDisabled ? "not-allowed" : "pointer",
                  border: isClicked
                    ? "2px solid #87CEEB"
                    : "2px solid transparent",
                }}
              >
                <span
                  style={{
                    fontSize: "18px",
                    marginRight: "4px",
                    display: "inline-flex",
                    alignItems: "center",
                    justifyContent: "center",
                    transition: "transform 0.15s ease",
                    transform: isClicked ? "scale(1.1)" : "scale(1)",
                  }}
                >
                  {ratingType.icon}
                </span>
                <span
                  className="fw-bold"
                  style={{
                    color: isActive ? "#fff" : "#666",
                    fontSize: "16px",
                    lineHeight: "1",
                    transition: "color 0.2s ease",
                  }}
                >
                  {ratings[ratingType.type]}
                </span>
              </div>
            </button>
          );
        })}
      </div>

      {loading && (
        <div className="text-center mt-2">
          <div className="spinner-border spinner-border-sm" role="status">
            <span className="visually-hidden">处理中...</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default RatingComponent;
